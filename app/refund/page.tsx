import Link from 'next/link';

export default function RefundPolicy() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="mb-8">
          <Link 
            href="/" 
            className="text-primary hover:text-primary/80 transition-colors mb-4 inline-block"
          >
            ← Back to Home
          </Link>
          <h1 className="text-4xl font-bold gradient-text mb-4">Refund and Returns Policy</h1>
          <p className="text-muted-foreground">Last updated: [Date]</p>
        </div>

        <div className="prose prose-invert max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">1. Refund Eligibility</h2>
            <p className="text-muted-foreground mb-4">
              We offer refunds for digital products within 30 days of purchase if you are not satisfied with your purchase.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">2. How to Request a Refund</h2>
            <p className="text-muted-foreground mb-4">
              To request a refund, please contact our customer service <NAME_EMAIL> with your order details 
              and reason for the refund request.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">3. Processing Time</h2>
            <p className="text-muted-foreground mb-4">
              Refunds will be processed within 5-7 business days after approval. The refund will be credited back to 
              your original payment method.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">4. Non-Refundable Items</h2>
            <p className="text-muted-foreground mb-4">
              Certain items are non-refundable, including but not limited to:
            </p>
            <ul className="list-disc list-inside text-muted-foreground mb-4 ml-4">
              <li>Digital products that have been downloaded and accessed</li>
              <li>Services that have been fully rendered</li>
              <li>Customized or personalized products</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">5. Contact Information</h2>
            <p className="text-muted-foreground mb-4">
              For any questions regarding our refund policy, please contact us:
            </p>
            <p className="text-muted-foreground">
              Email: <EMAIL><br />
              Phone: 9980137004
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
