"use client"

import Header from './components/Header';
import BookPreview from './components/BookPreview';
import ProductInfo from './components/ProductInfo';
import CtaButton from './components/CtaButton';
import ShippingInfo from './components/ShippingInfo';
import WhyItWorksSection from './components/WhyItWorksSection';
import { TableOfContentsSection } from './components/TableOfContentsSection';
import { AppleCardsCarouselDemo } from './components/AppCardsCarouselDemo';
import { FAQSection } from './components/FAQSection';
import Footer from './components/Footer';
import BuyNowButton from './components/BuyNowButton';



const PuppetMastersBible = () => {

  return (
    <div className="min-h-screen bg-black text-white font-['Plus_Jakarta_Sans']">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          <BookPreview />
          <div className="space-y-6 px-2 sm:px-5">
            <ProductInfo />
            <CtaButton />
          </div>
        </div>
      </div>

      <div className="space-y-12 sm:space-y-10 lg:space-y-14">
        <WhyItWorksSection />
        <BuyNowButton />
        <TableOfContentsSection />
        <AppleCardsCarouselDemo />
        <BuyNowButton />
        <FAQSection />
      </div>

      <Footer />
    </div>
  );
};

export default PuppetMastersBible;