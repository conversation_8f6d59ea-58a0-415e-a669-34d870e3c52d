import { useState } from "react";
import {
  CheckCircle,
  User,
  Shield,
  Package,
  Plus,
  Mail,
  BadgeDollarSign,
} from "lucide-react";

const WhyItWorksSection = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const sectionContent: Record<string, React.ReactElement> = {
    "Why It Works": (
      <div className="text-gray-300 space-y-4 leading-relaxed text-sm sm:text-base">
        <p className="text-base sm:text-lg text-white font-semibold">Most people treat networking like a social lottery.</p>
        <p>
          They hope to “click” with someone or stumble into opportunity. But high-impact connectors don’t rely on luck—
          they use systems grounded in psychology, behavioral science, and real-world pattern recognition.
        </p>
        <p>This book is built on those patterns.</p>
        <p>
          We’re not talking about outdated scripts or spammy cold messages—we’re talking about the neuroscience of trust,
          the psychology of attention, and the social mechanics of influence.
        </p>
        <div>
          <p className="text-white font-medium mb-2">You’ll learn how to:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Trigger trust and memorability within minutes—without oversharing or overtalking</li>
            <li>Read a room before saying a word, so you know exactly when to move, pause, or pivot</li>
            <li>Design your network like a strategic asset—not a contact list</li>
            <li>Host, follow-up, and lead conversations that deepen connection (and open unexpected doors)</li>
          </ul>
        </div>
        <p>
          This isn’t charm or charisma. It’s intentional connection design—so people remember you, want to work with you,
          and bring others your way.
        </p>
        <p className="italic text-gray-400">It works because it's not about playing games—it's about mastering how humans actually connect.</p>
      </div>
    ),

    "Who It's For": (
      <div className="text-gray-300 space-y-4 leading-relaxed text-sm sm:text-base">
        <p>This isn’t for passive readers or self-help shelf collectors.</p>
        <p>
          It’s a field manual for those who want to build actual leverage—not by pretending or performing, but by becoming
          someone others trust, remember, and move with.
        </p>
        <div>
          <p className="text-white font-medium mb-2">You’ll resonate with this if you’re:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              An entrepreneur or operator who wants to turn every room into a growth engine—by being the most strategic connector in it
            </li>
            <li>
              A professional tired of transactional handshakes and empty LinkedIn likes—looking for real allies and deals
            </li>
            <li>
              An introvert or thinker who wants to master group dynamics—without becoming someone you're not
            </li>
            <li>
              Anyone who wants to create a magnetic presence—where people remember your name, respect your time, and bring you opportunities
            </li>
          </ul>
        </div>
        <p className="italic text-gray-400">
          If you're still collecting business cards you'll never use—this isn’t for you.
        </p>
      </div>
    ),

    "The Ethics": (
      <div className="text-gray-300 leading-relaxed text-sm sm:text-base space-y-4">
        <p>
          Influence isn’t manipulation. This book teaches psychology-backed tools, but how you use them is up to you.
        </p>
        <p>
          We believe in clarity, transparency, and alignment. Everything here is meant for long-term trust and mutual benefit—
          not trickery or deception.
        </p>
      </div>
    ),

    "What's Included": (
      <ul className="text-gray-300 list-disc pl-6 space-y-2 text-sm sm:text-base">
        <li>Digital PDF & ePub files</li>
        <li>Real-world scripts, prompts, and playbooks</li>
        <li>Neural influence frameworks & templates</li>
        <li>Lifetime updates and access to new additions</li>
      </ul>
    ),

    "Digital Edition": (
      <div className="text-gray-300 leading-relaxed text-sm sm:text-base">
        <p>
          You’ll receive instant access to all materials in both PDF and ePub formats—optimized for desktop, mobile, and e-readers.
        </p>
      </div>
    ),

    "Money-Back Guarantee": (
      <div className="text-gray-300 space-y-4 leading-relaxed text-sm sm:text-base">
        <p className="font-medium text-white">💸 No-Risk Guarantee</p>
        <p>
          Use the playbook for 60 days. If you don’t feel more connected, more memorable, and more strategically visible—
          or don’t walk away with at least five real-world wins—just email us.
        </p>
        <p>
          Show us you tried. We’ll refund your money, no drama. This was written to give you the network we wish we had when we started.
        </p>
      </div>
    ),
  };

  const sectionIcons: Record<string, React.ReactElement> = {
    "Why It Works": <CheckCircle className="w-5 h-5 mr-2 text-gray-400" />,
    "Who It's For": <User className="w-5 h-5 mr-2 text-gray-400" />,
    "The Ethics": <Shield className="w-5 h-5 mr-2 text-gray-400" />,
    "What's Included": <Package className="w-5 h-5 mr-2 text-gray-400" />,
    "Digital Edition": <Mail className="w-5 h-5 mr-2 text-gray-400" />,
    "Money-Back Guarantee": <BadgeDollarSign className="w-5 h-5 mr-2 text-gray-400" />,
  };

  const sectionKeys = Object.keys(sectionContent);

  // Sections after which to show Buy Now buttons
  const sectionsWithButtons = ["Who It's For", "What's Included"];

  const BuyNowButton = () => (
    <div className="flex justify-center py-8">
      <button
        onClick={scrollToTop}
        className="bg-white text-black font-semibold py-3 px-8 rounded-md hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 transition-transform"
      >
        Buy Now - $49
      </button>
    </div>
  );

  return (
    <div className="bg-black py-10 sm:py-8 lg:py-10">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-10">
        <div className="text-center mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-8 text-start leading-tight text-white">
            The Dominion Network Playbook isn't about faking charm or playing status games.{" "}
            <span className="text-[#925bff]">
              It's about mastering human connection with science, psychology, and real-world tools that actually work.
            </span>
          </h2>
        </div>

        <div className="space-y-2">
          {sectionKeys.map((section, idx) => {
            const isExpanded = expandedSection === section;
            const isLast = idx === sectionKeys.length - 1;
            const showButton = sectionsWithButtons.includes(section);

            return (
              <div key={section}>
                <div className="bg-gray-900/30 rounded-lg overflow-hidden">
                  <button
                    className={`w-full py-6 sm:py-8 px-6 text-left flex items-center justify-between transition-colors hover:bg-gray-800/50 ${
                      !isExpanded && !isLast ? "" : ""
                    }`}
                    onClick={() => toggleSection(section)}
                  >
                    <div className="flex items-center font-semibold text-white text-sm sm:text-base">
                      {sectionIcons[section]}
                      <span className="ml-2">{section}</span>
                    </div>
                    <span
                      className="w-5 h-5 text-white flex items-center justify-center transition-transform duration-300"
                      style={{ transform: isExpanded ? "rotate(45deg)" : "rotate(0deg)" }}
                    >
                      <Plus className="w-5 h-5" />
                    </span>
                  </button>
                  {/* Animate dropdown open/close */}
                  <div
                    className="overflow-hidden transition-all duration-500"
                    style={{
                      maxHeight: isExpanded ? 1000 : 0,
                      opacity: isExpanded ? 1 : 0,
                      transitionProperty: "max-height, opacity",
                    }}
                    aria-hidden={!isExpanded}
                  >
                    <div className={isExpanded ? "px-6 pb-6 sm:pb-8" : ""}>
                      {isExpanded && sectionContent[section]}
                    </div>
                  </div>
                </div>

                {/* Add Buy Now button after specific sections */}
                {showButton && <BuyNowButton />}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default WhyItWorksSection;
