import { useState } from "react";
import { Plus } from "lucide-react";

export const FAQSection = () => {
  // Use an array of booleans to track open/closed state for each FAQ
  const [openIndexes, setOpenIndexes] = useState<boolean[]>([]);

  const faqs = [
    {
      question: "Who is this eBook for?",
      answer: "This playbook is for entrepreneurs, creators, professionals, and introverts who want to build meaningful relationships—without being fake, forceful, or overly extroverted. If you've ever felt like networking was cringey or inauthentic, this book is your antidote."
    },
    {
      question: "Is this just another book on \"how to network\"?",
      answer: "No. This isn't recycled advice from 2005. This is a modern, neuroscience-backed guide rooted in personal experience, psychological insights, real-world scripts, and social strategy. You'll find no fluff—just high-impact ideas that work."
    },
    {
      question: "I'm introverted. Will this book still help me?",
      answer: "Absolutely. In fact, it's designed especially for introverts who want to show up with quiet confidence. You'll learn how to leverage observation, timing, and authentic curiosity—without pretending to be someone you're not."
    },
    {
      question: "Is this just theory or does it include practical stuff?",
      answer: "Every chapter ends with ready-to-use prompts, tools, templates, and real-life examples. You'll get conversation openers, email/DM structures, group dynamics playbooks, and visual frameworks."
    },
    {
      question: "How long is the book? Will I actually finish it?",
      answer: "Yes. It's intentionally short, punchy, and high-signal. No filler. No tangents. Every page gives you something you can apply. Most readers finish it in under 2 hours, and start applying it the same day."
    },
    {
      question: "What format is the eBook delivered in?",
      answer: "The book is delivered as a downloadable PDF and mobile-friendly format so you can read it on any device—desktop, tablet, or phone."
    },
    {
      question: "Is there a refund policy?",
      answer: "Yes. If you don't find actionable value in the book, reach out within 60 days with proof of effort and I'll refund you. No hoops. No awkwardness. Just fairness."
    },
    {
      question: "Do I need a big following or business to use this?",
      answer: "Not at all. This book is about strategy and psychology, not popularity. Whether you're starting out or scaling up, these tools will help you stand out and stay remembered."
    }
  ];

  // Ensure openIndexes has the correct length
  // (initialize all to false if not already set)
  if (openIndexes.length !== faqs.length) {
    // This is safe because React will re-render after setState
    setOpenIndexes(Array(faqs.length).fill(false));
  }

  const toggleFAQ = (index: number) => {
    setOpenIndexes((prev) => {
      const newOpenIndexes = [...prev];
      newOpenIndexes[index] = !newOpenIndexes[index];
      return newOpenIndexes;
    });
  };

  return (
    <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 mb-10">
      <div className="max-w-6xl mx-auto">
        <div className="text-start mb-12 sm:mb-16 fade-in-up">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold mb-6 leading-tight">
            Frequently Asked <span className="gradient-text">Questions</span> (FAQs)
          </h2>
          {/* <p className="text-xl text-muted-foreground text-start">
            Everything you need to know before you start
          </p> */}
        </div>

        <div className="border py-4 sm:py-6 px-4 sm:px-6 lg:px-8 border-gray-700 rounded-xl overflow-hidden fade-in-up" style={{animationDelay: '0.2s'}}>
          {faqs.map((faq, index) => {
            const isOpen = openIndexes[index];
            const isLast = index === faqs.length - 1;
            return (
              <div
                key={index}
                className="overflow-hidden fade-in-up"
                style={{ animationDelay: `${0.3 + index * 0.1}s` }}
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className={`w-full py-6 sm:py-8 lg:py-10 text-left flex items-center justify-between gap-4 ${
                    // Only show border under button if not open and not last
                    !isOpen && !isLast ? "border-b border-gray-700" : ""
                  }`}
                >
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-foreground pr-2 sm:pr-4 leading-relaxed">
                    {faq.question}
                  </h3>
                  {/* Use Plus icon with rotation animation when open */}
                  <span
                    className={`transition-transform duration-300 w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center text-foreground flex-shrink-0`}
                    style={{
                      transform: isOpen ? "rotate(45deg)" : "rotate(0deg)",
                    }}
                  >
                    <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
                  </span>
                </button>

                {/* Animate dropdown open/close */}
                <div
                  className="overflow-hidden transition-all duration-500"
                  style={{
                    maxHeight: isOpen ? 500 : 0,
                    opacity: isOpen ? 1 : 0,
                    transitionProperty: "max-height, opacity",
                  }}
                  aria-hidden={!isOpen}
                >
                  <div className={isOpen ? "pb-6 sm:pb-8 lg:pb-10" : ""}>
                    {isOpen && (
                      <p className="text-foreground leading-relaxed text-sm sm:text-base">
                        {faq.answer}
                      </p>
                    )}
                  </div>
                  {/* Show border after content if open and not last */}
                  {isOpen && !isLast && (
                    <div className="border-b border-gray-700" />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};